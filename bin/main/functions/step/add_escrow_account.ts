import { execSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, SRC_ZONE_ID, ESCROW_ACCOUNT } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * 指定環境の指定ビジネスゾーンにエスクローアカウントを追加する
 * @param network 環境名
 * @param bizZoneId ビジネスゾーンID
 */
export async function addEscrowAccount(network: string, bizZoneId: number) {
  try {
    if (!network) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    // ビジネスゾーンIDの検証
    if (isNaN(bizZoneId) || bizZoneId < 3001 || bizZoneId > 3999) {
      message('err', 'Please specify a valid BIZONE_ID between 3001 and 3999.')
      process.exit(1)
    }

    message('info', `Registering escrow id to bridge for Zone: ${bizZoneId}`)

    // hardhatタスク実行
    const command = `npx hardhat registerEscrowAcc --src-zone-id ${SRC_ZONE_ID} --dst-zone-id ${bizZoneId} --escrow-account ${ESCROW_ACCOUNT} --network ${network}`

    execSync(command, { stdio: 'inherit' })

    message('success', `Successfully registered the escrow account for network: ${network} completed.`)
  } catch (err) {
    message('err', `Failed to execute Hardhat command: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const bizZoneId = process.argv[3]
  if (!network || !bizZoneId) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name] [BIZZONE_ID]`)
    process.exit(1)
  }

  addEscrowAccount(network, Number(bizZoneId)).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
